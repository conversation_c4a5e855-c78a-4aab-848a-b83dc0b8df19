from rembg import remove
from PIL import Image, UnidentifiedImageError
import os
import traceback

input_folder = 'originals'
output_folder = 'output'

os.makedirs(output_folder, exist_ok=True)

processed = 0
failed = 0

for file_name in os.listdir(input_folder):
    if file_name.lower().endswith(('.png', '.jpg', '.jpeg')):
        input_path = os.path.join(input_folder, file_name)
        base_name = os.path.splitext(file_name)[0]
        output_path = os.path.join(output_folder, f"{base_name}.png")

        try:
            with Image.open(input_path) as img:
                output = remove(img)
                output.save(output_path, format='PNG')
            print(f"[SUCCESS] Processed {file_name}")
            processed += 1
        except UnidentifiedImageError:
            print(f"[ERROR] Failed to process {file_name}: Invalid image file")
            failed += 1
        except Exception as e:
            print(f"[ERROR] Failed to process {file_name}: {str(e)}")
            traceback.print_exc()
            failed += 1

print(f"\nProcessing complete. Success: {processed}, Failed: {failed}")